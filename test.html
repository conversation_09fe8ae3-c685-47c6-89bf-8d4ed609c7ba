<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat - Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
        }
    </script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function App() {
            const [messages, setMessages] = useState([]);
            const [inputMessage, setInputMessage] = useState('');
            const [isLoading, setIsLoading] = useState(false);
            const [apiKey, setApiKey] = useState('');
            const [showSettings, setShowSettings] = useState(false);
            const [darkMode, setDarkMode] = useState(true);

            useEffect(() => {
                const savedApiKey = localStorage.getItem('deepseek-api-key');
                const savedTheme = localStorage.getItem('deepseek-theme');
                
                if (savedApiKey) {
                    setApiKey(savedApiKey);
                }
                
                if (savedTheme) {
                    setDarkMode(savedTheme === 'dark');
                }
            }, []);

            useEffect(() => {
                if (darkMode) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
                localStorage.setItem('deepseek-theme', darkMode ? 'dark' : 'light');
            }, [darkMode]);

            const saveApiKey = () => {
                localStorage.setItem('deepseek-api-key', apiKey);
                setShowSettings(false);
            };

            const sendMessage = async () => {
                if (!inputMessage.trim() || !apiKey.trim()) {
                    if (!apiKey.trim()) {
                        setShowSettings(true);
                    }
                    return;
                }

                const userMessage = {
                    id: Date.now().toString(),
                    role: 'user',
                    content: inputMessage,
                    timestamp: new Date(),
                };

                setMessages(prev => [...prev, userMessage]);
                setInputMessage('');
                setIsLoading(true);

                try {
                    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`,
                            'Content-Type': 'application/json',
                            'HTTP-Referer': window.location.origin,
                            'X-Title': 'DeepSeek Chat',
                        },
                        body: JSON.stringify({
                            model: 'deepseek/deepseek-r1-0528:free',
                            messages: [
                                ...messages.map(msg => ({
                                    role: msg.role,
                                    content: msg.content,
                                })),
                                {
                                    role: 'user',
                                    content: inputMessage,
                                },
                            ],
                        }),
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    const assistantMessage = {
                        id: (Date.now() + 1).toString(),
                        role: 'assistant',
                        content: data.choices[0].message.content,
                        timestamp: new Date(),
                    };

                    setMessages(prev => [...prev, assistantMessage]);
                } catch (error) {
                    console.error('Error sending message:', error);
                    const errorMessage = {
                        id: (Date.now() + 1).toString(),
                        role: 'assistant',
                        content: 'عذراً، حدث خطأ في الاتصال بالخدمة. يرجى التحقق من مفتاح API والمحاولة مرة أخرى.',
                        timestamp: new Date(),
                    };
                    setMessages(prev => [...prev, errorMessage]);
                } finally {
                    setIsLoading(false);
                }
            };

            const handleKeyPress = (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            };

            return (
                <div className={`min-h-screen transition-colors duration-200 ${
                    darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'
                }`}>
                    {/* Header */}
                    <header className={`border-b transition-colors duration-200 ${
                        darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
                    }`}>
                        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
                            <h1 className="text-2xl font-bold">DeepSeek Chat</h1>
                            <div className="flex items-center gap-2">
                                <button
                                    onClick={() => setDarkMode(!darkMode)}
                                    className={`p-2 rounded-lg transition-colors duration-200 ${
                                        darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                    }`}
                                >
                                    {darkMode ? '☀️' : '🌙'}
                                </button>
                                <button
                                    onClick={() => setShowSettings(!showSettings)}
                                    className={`p-2 rounded-lg transition-colors duration-200 ${
                                        darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                    }`}
                                >
                                    ⚙️
                                </button>
                            </div>
                        </div>
                    </header>

                    {/* Settings Panel */}
                    {showSettings && (
                        <div className={`border-b transition-colors duration-200 ${
                            darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
                        }`}>
                            <div className="max-w-4xl mx-auto px-4 py-4">
                                <h2 className="text-lg font-semibold mb-3">الإعدادات</h2>
                                <div className="flex flex-col gap-3">
                                    <div>
                                        <label className="block text-sm font-medium mb-2">
                                            مفتاح API (OpenRouter)
                                        </label>
                                        <input
                                            type="password"
                                            value={apiKey}
                                            onChange={(e) => setApiKey(e.target.value)}
                                            placeholder="أدخل مفتاح API الخاص بك"
                                            className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                                                darkMode 
                                                    ? 'bg-gray-700 border-gray-600 focus:border-blue-500' 
                                                    : 'bg-white border-gray-300 focus:border-blue-500'
                                            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
                                        />
                                    </div>
                                    <div className="flex gap-2">
                                        <button
                                            onClick={saveApiKey}
                                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                                        >
                                            حفظ
                                        </button>
                                        <button
                                            onClick={() => setShowSettings(false)}
                                            className={`px-4 py-2 rounded-lg transition-colors duration-200 ${
                                                darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
                                            }`}
                                        >
                                            إلغاء
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Chat Messages */}
                    <main className="max-w-4xl mx-auto px-4 py-6 flex-1">
                        <div className="space-y-4 mb-6">
                            {messages.length === 0 ? (
                                <div className="text-center py-12">
                                    <h2 className="text-xl font-semibold mb-2">مرحباً بك في DeepSeek Chat</h2>
                                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                        ابدأ محادثة مع الذكاء الاصطناعي DeepSeek R1
                                    </p>
                                </div>
                            ) : (
                                messages.map((message) => (
                                    <div
                                        key={message.id}
                                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                                    >
                                        <div
                                            className={`max-w-3xl px-4 py-3 rounded-lg ${
                                                message.role === 'user'
                                                    ? 'bg-blue-600 text-white'
                                                    : darkMode
                                                    ? 'bg-gray-800 border border-gray-700'
                                                    : 'bg-white border border-gray-200'
                                            }`}
                                        >
                                            <p className="whitespace-pre-wrap">{message.content}</p>
                                        </div>
                                    </div>
                                ))
                            )}
                            
                            {isLoading && (
                                <div className="flex justify-start">
                                    <div className={`max-w-3xl px-4 py-3 rounded-lg ${
                                        darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
                                    }`}>
                                        <div className="flex items-center gap-2">
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                                            <span>جاري الكتابة...</span>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </main>

                    {/* Input Area */}
                    <footer className={`border-t transition-colors duration-200 ${
                        darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
                    }`}>
                        <div className="max-w-4xl mx-auto px-4 py-4">
                            <div className="flex gap-2">
                                <textarea
                                    value={inputMessage}
                                    onChange={(e) => setInputMessage(e.target.value)}
                                    onKeyPress={handleKeyPress}
                                    placeholder="اكتب رسالتك هنا..."
                                    className={`flex-1 px-3 py-2 rounded-lg border resize-none transition-colors duration-200 ${
                                        darkMode 
                                            ? 'bg-gray-700 border-gray-600 focus:border-blue-500' 
                                            : 'bg-white border-gray-300 focus:border-blue-500'
                                    } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
                                    rows={1}
                                    style={{ minHeight: '40px', maxHeight: '120px' }}
                                />
                                <button
                                    onClick={sendMessage}
                                    disabled={isLoading || !inputMessage.trim()}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                >
                                    📤
                                </button>
                            </div>
                        </div>
                    </footer>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
