import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingMessageProps {
  darkMode: boolean;
}

const LoadingMessage: React.FC<LoadingMessageProps> = ({ darkMode }) => {
  return (
    <div className="flex justify-start">
      <div className={`max-w-3xl px-4 py-3 rounded-lg ${
        darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
      }`}>
        <div className="flex items-center gap-2">
          <Loader2 size={16} className="animate-spin text-blue-500" />
          <span className="text-sm">جاري الكتابة...</span>
        </div>
      </div>
    </div>
  );
};

export default LoadingMessage;
