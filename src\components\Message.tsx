import React from 'react';
import ReactMarkdown from 'react-markdown';

interface MessageProps {
  message: {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  };
  darkMode: boolean;
}

const Message: React.FC<MessageProps> = ({ message, darkMode }) => {
  return (
    <div
      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
    >
      <div
        className={`max-w-3xl px-4 py-3 rounded-lg ${
          message.role === 'user'
            ? 'bg-blue-600 text-white'
            : darkMode
            ? 'bg-gray-800 border border-gray-700'
            : 'bg-white border border-gray-200'
        }`}
      >
        {message.role === 'assistant' ? (
          <ReactMarkdown 
            className="prose prose-sm max-w-none dark:prose-invert"
            components={{
              code: ({ node, inline, className, children, ...props }) => {
                const match = /language-(\w+)/.exec(className || '');
                return !inline ? (
                  <pre className={`${darkMode ? 'bg-gray-900' : 'bg-gray-100'} p-3 rounded-md overflow-x-auto`}>
                    <code className={className} {...props}>
                      {children}
                    </code>
                  </pre>
                ) : (
                  <code className={`${darkMode ? 'bg-gray-700' : 'bg-gray-200'} px-1 py-0.5 rounded text-sm`} {...props}>
                    {children}
                  </code>
                );
              },
            }}
          >
            {message.content}
          </ReactMarkdown>
        ) : (
          <p className="whitespace-pre-wrap">{message.content}</p>
        )}
        <div className={`text-xs mt-2 opacity-70 ${
          message.role === 'user' ? 'text-blue-100' : darkMode ? 'text-gray-400' : 'text-gray-500'
        }`}>
          {message.timestamp.toLocaleTimeString('ar-SA', { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </div>
      </div>
    </div>
  );
};

export default Message;
