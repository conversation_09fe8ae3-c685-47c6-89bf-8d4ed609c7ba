<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
        }
    </script>
    <style>
        /* تحسينات للكود */
        .prose pre {
            position: relative;
            background: #1e1e1e !important;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
        }

        .prose code {
            background: rgba(110, 118, 129, 0.4);
            padding: 2px 4px;
            border-radius: 4px;
            font-size: 0.875em;
        }

        .prose pre code {
            background: transparent;
            padding: 0;
        }

        /* تحسين التمرير */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.5);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.8);
        }

        /* تحسين الانيميشن */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* تحسين textarea */
        textarea {
            resize: none;
            transition: height 0.2s ease;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        function App() {
            const [messages, setMessages] = useState([]);
            const [inputMessage, setInputMessage] = useState('');
            const [isLoading, setIsLoading] = useState(false);
            const [apiKey, setApiKey] = useState('');
            const [showSettings, setShowSettings] = useState(false);
            const [darkMode, setDarkMode] = useState(true);
            const [conversations, setConversations] = useState([]);
            const [currentConversationId, setCurrentConversationId] = useState(null);
            const [showConversations, setShowConversations] = useState(false);
            const [conversationTitle, setConversationTitle] = useState('محادثة جديدة');
            const messagesEndRef = useRef(null);

            useEffect(() => {
                const savedApiKey = localStorage.getItem('deepseek-api-key');
                const savedTheme = localStorage.getItem('deepseek-theme');
                const savedConversations = localStorage.getItem('deepseek-conversations');

                if (savedApiKey) {
                    setApiKey(savedApiKey);
                }

                if (savedTheme) {
                    setDarkMode(savedTheme === 'dark');
                }

                if (savedConversations) {
                    setConversations(JSON.parse(savedConversations));
                }
            }, []);

            useEffect(() => {
                messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }, [messages]);

            useEffect(() => {
                localStorage.setItem('deepseek-conversations', JSON.stringify(conversations));
            }, [conversations]);

            useEffect(() => {
                if (darkMode) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
                localStorage.setItem('deepseek-theme', darkMode ? 'dark' : 'light');
            }, [darkMode]);

            const saveApiKey = () => {
                localStorage.setItem('deepseek-api-key', apiKey);
                setShowSettings(false);
            };

            const saveConversation = () => {
                if (messages.length === 0) return;

                const conversation = {
                    id: currentConversationId || Date.now().toString(),
                    title: conversationTitle,
                    messages: messages,
                    timestamp: new Date().toISOString(),
                };

                const updatedConversations = currentConversationId
                    ? conversations.map(conv => conv.id === currentConversationId ? conversation : conv)
                    : [...conversations, conversation];

                setConversations(updatedConversations);
                setCurrentConversationId(conversation.id);
            };

            const loadConversation = (conversation) => {
                setMessages(conversation.messages);
                setCurrentConversationId(conversation.id);
                setConversationTitle(conversation.title);
                setShowConversations(false);
            };

            const newConversation = () => {
                setMessages([]);
                setCurrentConversationId(null);
                setConversationTitle('محادثة جديدة');
                setShowConversations(false);
            };

            const deleteConversation = (conversationId) => {
                const updatedConversations = conversations.filter(conv => conv.id !== conversationId);
                setConversations(updatedConversations);

                if (currentConversationId === conversationId) {
                    newConversation();
                }
            };

            const copyToClipboard = (text) => {
                navigator.clipboard.writeText(text).then(() => {
                    // يمكن إضافة إشعار هنا
                });
            };

            const exportConversation = () => {
                const data = {
                    title: conversationTitle,
                    messages: messages,
                    timestamp: new Date().toISOString()
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${conversationTitle}.json`;
                a.click();
                URL.revokeObjectURL(url);
            };

            const clearAllConversations = () => {
                if (confirm('هل أنت متأكد من حذف جميع المحادثات؟')) {
                    setConversations([]);
                    newConversation();
                }
            };

            const sendMessage = async () => {
                if (!inputMessage.trim() || !apiKey.trim()) {
                    if (!apiKey.trim()) {
                        setShowSettings(true);
                    }
                    return;
                }

                const userMessage = {
                    id: Date.now().toString(),
                    role: 'user',
                    content: inputMessage,
                    timestamp: new Date(),
                };

                setMessages(prev => [...prev, userMessage]);
                setInputMessage('');
                setIsLoading(true);

                try {
                    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`,
                            'Content-Type': 'application/json',
                            'HTTP-Referer': window.location.origin,
                            'X-Title': 'DeepSeek Chat',
                        },
                        body: JSON.stringify({
                            model: 'deepseek/deepseek-r1-0528:free',
                            messages: [
                                ...messages.map(msg => ({
                                    role: msg.role,
                                    content: msg.content,
                                })),
                                {
                                    role: 'user',
                                    content: inputMessage,
                                },
                            ],
                        }),
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    const assistantMessage = {
                        id: (Date.now() + 1).toString(),
                        role: 'assistant',
                        content: data.choices[0].message.content,
                        timestamp: new Date(),
                    };

                    setMessages(prev => [...prev, assistantMessage]);

                    // حفظ المحادثة تلقائياً
                    setTimeout(() => {
                        saveConversation();
                    }, 100);
                } catch (error) {
                    console.error('Error sending message:', error);
                    const errorMessage = {
                        id: (Date.now() + 1).toString(),
                        role: 'assistant',
                        content: 'عذراً، حدث خطأ في الاتصال بالخدمة. يرجى التحقق من مفتاح API والمحاولة مرة أخرى.',
                        timestamp: new Date(),
                    };
                    setMessages(prev => [...prev, errorMessage]);
                } finally {
                    setIsLoading(false);
                }
            };

            const handleKeyPress = (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            };

            const MessageComponent = ({ message }) => {
                const [showCopyButton, setShowCopyButton] = useState(false);

                const renderContent = (content) => {
                    if (message.role === 'assistant') {
                        // تحويل Markdown إلى HTML
                        const htmlContent = marked.parse(content);

                        return (
                            <div
                                className="prose prose-sm max-w-none dark:prose-invert"
                                dangerouslySetInnerHTML={{ __html: htmlContent }}
                                onMouseEnter={() => setShowCopyButton(true)}
                                onMouseLeave={() => setShowCopyButton(false)}
                            />
                        );
                    } else {
                        return <p className="whitespace-pre-wrap">{content}</p>;
                    }
                };

                useEffect(() => {
                    // تطبيق syntax highlighting على الأكواد
                    if (message.role === 'assistant') {
                        Prism.highlightAll();
                    }
                }, [message.content]);

                return (
                    <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div
                            className={`max-w-3xl px-4 py-3 rounded-lg relative group ${
                                message.role === 'user'
                                    ? 'bg-blue-600 text-white'
                                    : darkMode
                                    ? 'bg-gray-800 border border-gray-700'
                                    : 'bg-white border border-gray-200'
                            }`}
                            onMouseEnter={() => setShowCopyButton(true)}
                            onMouseLeave={() => setShowCopyButton(false)}
                        >
                            {renderContent(message.content)}

                            {/* أزرار الإجراءات */}
                            <div className={`absolute top-2 left-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200`}>
                                <button
                                    onClick={() => copyToClipboard(message.content)}
                                    className={`p-1 rounded text-xs ${
                                        darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
                                    }`}
                                    title="نسخ النص"
                                >
                                    📋
                                </button>
                                {message.role === 'assistant' && (
                                    <button
                                        onClick={() => {
                                            const codeBlocks = message.content.match(/```[\s\S]*?```/g);
                                            if (codeBlocks) {
                                                const code = codeBlocks.map(block =>
                                                    block.replace(/```\w*\n?/g, '').replace(/```$/g, '')
                                                ).join('\n\n');
                                                copyToClipboard(code);
                                            }
                                        }}
                                        className={`p-1 rounded text-xs ${
                                            darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
                                        }`}
                                        title="نسخ الكود فقط"
                                    >
                                        💻
                                    </button>
                                )}
                            </div>

                            <div className={`text-xs mt-2 opacity-70 ${
                                message.role === 'user' ? 'text-blue-100' : darkMode ? 'text-gray-400' : 'text-gray-500'
                            }`}>
                                {message.timestamp.toLocaleTimeString('ar-SA', {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                })}
                            </div>
                        </div>
                    </div>
                );
            };

            return (
                <div className={`min-h-screen transition-colors duration-200 ${
                    darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'
                }`}>
                    {/* Header */}
                    <header className={`border-b transition-colors duration-200 ${
                        darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
                    }`}>
                        <div className="max-w-6xl mx-auto px-4 py-4 flex items-center justify-between">
                            <div className="flex items-center gap-4">
                                <h1 className="text-2xl font-bold">DeepSeek Chat</h1>
                                <input
                                    type="text"
                                    value={conversationTitle}
                                    onChange={(e) => setConversationTitle(e.target.value)}
                                    className={`px-2 py-1 text-sm rounded border ${
                                        darkMode
                                            ? 'bg-gray-700 border-gray-600'
                                            : 'bg-white border-gray-300'
                                    } focus:outline-none focus:ring-1 focus:ring-blue-500`}
                                    placeholder="عنوان المحادثة"
                                />
                            </div>
                            <div className="flex items-center gap-2">
                                <button
                                    onClick={newConversation}
                                    className={`p-2 rounded-lg transition-colors duration-200 ${
                                        darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                    }`}
                                    title="محادثة جديدة"
                                >
                                    ➕
                                </button>
                                <button
                                    onClick={() => setShowConversations(!showConversations)}
                                    className={`p-2 rounded-lg transition-colors duration-200 ${
                                        darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                    }`}
                                    title="المحادثات السابقة"
                                >
                                    📁
                                </button>
                                <button
                                    onClick={exportConversation}
                                    disabled={messages.length === 0}
                                    className={`p-2 rounded-lg transition-colors duration-200 disabled:opacity-50 ${
                                        darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                    }`}
                                    title="تصدير المحادثة"
                                >
                                    📥
                                </button>
                                <button
                                    onClick={() => setDarkMode(!darkMode)}
                                    className={`p-2 rounded-lg transition-colors duration-200 ${
                                        darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                    }`}
                                    title="تبديل الوضع"
                                >
                                    {darkMode ? '☀️' : '🌙'}
                                </button>
                                <button
                                    onClick={() => setShowSettings(!showSettings)}
                                    className={`p-2 rounded-lg transition-colors duration-200 ${
                                        darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                    }`}
                                    title="الإعدادات"
                                >
                                    ⚙️
                                </button>
                            </div>
                        </div>
                    </header>

                    {/* Conversations Panel */}
                    {showConversations && (
                        <div className={`border-b transition-colors duration-200 ${
                            darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
                        }`}>
                            <div className="max-w-6xl mx-auto px-4 py-4">
                                <div className="flex items-center justify-between mb-3">
                                    <h2 className="text-lg font-semibold">المحادثات السابقة</h2>
                                    <button
                                        onClick={clearAllConversations}
                                        className="text-red-500 hover:text-red-600 text-sm"
                                    >
                                        حذف الكل
                                    </button>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
                                    {conversations.length === 0 ? (
                                        <p className={`col-span-full text-center py-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                            لا توجد محادثات محفوظة
                                        </p>
                                    ) : (
                                        conversations.map((conversation) => (
                                            <div
                                                key={conversation.id}
                                                className={`p-3 rounded-lg border cursor-pointer transition-colors duration-200 ${
                                                    darkMode
                                                        ? 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                                                        : 'bg-white border-gray-300 hover:bg-gray-50'
                                                } ${currentConversationId === conversation.id ? 'ring-2 ring-blue-500' : ''}`}
                                                onClick={() => loadConversation(conversation)}
                                            >
                                                <div className="flex items-center justify-between mb-2">
                                                    <h3 className="font-medium text-sm truncate">{conversation.title}</h3>
                                                    <button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            deleteConversation(conversation.id);
                                                        }}
                                                        className="text-red-500 hover:text-red-600 text-xs"
                                                    >
                                                        🗑️
                                                    </button>
                                                </div>
                                                <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                                    {conversation.messages.length} رسالة
                                                </p>
                                                <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                                                    {new Date(conversation.timestamp).toLocaleDateString('ar-SA')}
                                                </p>
                                            </div>
                                        ))
                                    )}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Settings Panel */}
                    {showSettings && (
                        <div className={`border-b transition-colors duration-200 ${
                            darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
                        }`}>
                            <div className="max-w-6xl mx-auto px-4 py-4">
                                <h2 className="text-lg font-semibold mb-3">الإعدادات</h2>
                                <div className="flex flex-col gap-3">
                                    <div>
                                        <label className="block text-sm font-medium mb-2">
                                            مفتاح API (OpenRouter)
                                        </label>
                                        <input
                                            type="password"
                                            value={apiKey}
                                            onChange={(e) => setApiKey(e.target.value)}
                                            placeholder="أدخل مفتاح API الخاص بك"
                                            className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                                                darkMode
                                                    ? 'bg-gray-700 border-gray-600 focus:border-blue-500'
                                                    : 'bg-white border-gray-300 focus:border-blue-500'
                                            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
                                        />
                                        <p className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                            احصل على مفتاح API من{' '}
                                            <a
                                                href="https://openrouter.ai/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-500 hover:text-blue-600 underline"
                                            >
                                                OpenRouter
                                            </a>
                                        </p>
                                    </div>
                                    <div className="flex gap-2">
                                        <button
                                            onClick={saveApiKey}
                                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                                        >
                                            حفظ
                                        </button>
                                        <button
                                            onClick={() => setShowSettings(false)}
                                            className={`px-4 py-2 rounded-lg transition-colors duration-200 ${
                                                darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
                                            }`}
                                        >
                                            إلغاء
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Chat Messages */}
                    <main className="max-w-6xl mx-auto px-4 py-6 flex-1">
                        <div className="space-y-4 mb-6">
                            {messages.length === 0 ? (
                                <div className="text-center py-12">
                                    <h2 className="text-xl font-semibold mb-2">مرحباً بك في DeepSeek Chat</h2>
                                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                                        ابدأ محادثة مع الذكاء الاصطناعي DeepSeek R1
                                    </p>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                                        <div className={`p-4 rounded-lg border ${
                                            darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                                        }`}>
                                            <h3 className="font-semibold mb-2">💡 أمثلة للأسئلة</h3>
                                            <ul className={`text-sm space-y-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                                <li>• اكتب لي كود Python لحساب الأرقام الأولية</li>
                                                <li>• اشرح لي مفهوم الذكاء الاصطناعي</li>
                                                <li>• ساعدني في حل مشكلة برمجية</li>
                                            </ul>
                                        </div>
                                        <div className={`p-4 rounded-lg border ${
                                            darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                                        }`}>
                                            <h3 className="font-semibold mb-2">🚀 المميزات</h3>
                                            <ul className={`text-sm space-y-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                                <li>• دعم Markdown والأكواد</li>
                                                <li>• حفظ المحادثات تلقائياً</li>
                                                <li>• نسخ الأكواد بسهولة</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                messages.map((message) => (
                                    <MessageComponent key={message.id} message={message} />
                                ))
                            )}

                            {isLoading && (
                                <div className="flex justify-start">
                                    <div className={`max-w-3xl px-4 py-3 rounded-lg ${
                                        darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
                                    }`}>
                                        <div className="flex items-center gap-2">
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                                            <span>جاري الكتابة...</span>
                                        </div>
                                    </div>
                                </div>
                            )}
                            <div ref={messagesEndRef} />
                        </div>
                    </main>

                    {/* Input Area */}
                    <footer className={`border-t transition-colors duration-200 ${
                        darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
                    }`}>
                        <div className="max-w-6xl mx-auto px-4 py-4">
                            {/* أدوات سريعة */}
                            <div className="flex gap-2 mb-3 flex-wrap">
                                <button
                                    onClick={() => setInputMessage('اكتب لي كود Python لـ ')}
                                    className={`px-3 py-1 text-xs rounded-full border transition-colors duration-200 ${
                                        darkMode
                                            ? 'border-gray-600 hover:bg-gray-700'
                                            : 'border-gray-300 hover:bg-gray-100'
                                    }`}
                                >
                                    🐍 Python
                                </button>
                                <button
                                    onClick={() => setInputMessage('اكتب لي كود JavaScript لـ ')}
                                    className={`px-3 py-1 text-xs rounded-full border transition-colors duration-200 ${
                                        darkMode
                                            ? 'border-gray-600 hover:bg-gray-700'
                                            : 'border-gray-300 hover:bg-gray-100'
                                    }`}
                                >
                                    🟨 JavaScript
                                </button>
                                <button
                                    onClick={() => setInputMessage('اشرح لي مفهوم ')}
                                    className={`px-3 py-1 text-xs rounded-full border transition-colors duration-200 ${
                                        darkMode
                                            ? 'border-gray-600 hover:bg-gray-700'
                                            : 'border-gray-300 hover:bg-gray-100'
                                    }`}
                                >
                                    📚 شرح
                                </button>
                                <button
                                    onClick={() => setInputMessage('ساعدني في حل مشكلة ')}
                                    className={`px-3 py-1 text-xs rounded-full border transition-colors duration-200 ${
                                        darkMode
                                            ? 'border-gray-600 hover:bg-gray-700'
                                            : 'border-gray-300 hover:bg-gray-100'
                                    }`}
                                >
                                    🔧 حل مشكلة
                                </button>
                                <button
                                    onClick={() => setInputMessage('راجع هذا الكود وحسنه:\n```\n\n```')}
                                    className={`px-3 py-1 text-xs rounded-full border transition-colors duration-200 ${
                                        darkMode
                                            ? 'border-gray-600 hover:bg-gray-700'
                                            : 'border-gray-300 hover:bg-gray-100'
                                    }`}
                                >
                                    ✨ مراجعة كود
                                </button>
                            </div>

                            <div className="flex gap-2">
                                <div className="flex-1 relative">
                                    <textarea
                                        value={inputMessage}
                                        onChange={(e) => setInputMessage(e.target.value)}
                                        onKeyPress={handleKeyPress}
                                        placeholder="اكتب رسالتك هنا... (اضغط Enter للإرسال، Shift+Enter لسطر جديد)"
                                        className={`w-full px-3 py-2 rounded-lg border resize-none transition-colors duration-200 ${
                                            darkMode
                                                ? 'bg-gray-700 border-gray-600 focus:border-blue-500'
                                                : 'bg-white border-gray-300 focus:border-blue-500'
                                        } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
                                        rows={1}
                                        style={{ minHeight: '40px', maxHeight: '120px' }}
                                    />
                                    <div className={`absolute bottom-2 left-2 text-xs ${
                                        darkMode ? 'text-gray-500' : 'text-gray-400'
                                    }`}>
                                        {inputMessage.length}/4000
                                    </div>
                                </div>
                                <div className="flex flex-col gap-2">
                                    <button
                                        onClick={() => {
                                            if (messages.length > 0) {
                                                saveConversation();
                                            }
                                        }}
                                        disabled={messages.length === 0}
                                        className={`p-2 rounded-lg transition-colors duration-200 disabled:opacity-50 ${
                                            darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                        }`}
                                        title="حفظ المحادثة"
                                    >
                                        💾
                                    </button>
                                    <button
                                        onClick={sendMessage}
                                        disabled={isLoading || !inputMessage.trim()}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                    >
                                        {isLoading ? '⏳' : '📤'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </footer>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
