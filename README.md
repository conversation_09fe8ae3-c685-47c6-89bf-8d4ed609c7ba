# DeepSeek Chat

تطبيق محادثة ذكي يعمل بتقنية الذكاء الاصطناعي باستخدام نموذج DeepSeek R1.

## المميزات

- 🤖 محادثة مع نموذج DeepSeek R1 عبر OpenRouter API
- 🌙 دعم الوضع الداكن والفاتح
- 📝 دعم عرض الردود بتنسيق Markdown
- 🔑 إدخال مفتاح API من واجهة المستخدم
- 💾 حفظ الإعدادات محلياً
- 📱 تصميم متجاوب

## التقنيات المستخدمة

- **Frontend**: React + TypeScript + Tailwind CSS + Vite
- **API**: OpenRouter (DeepSeek R1)
- **UI Icons**: Lucide React
- **Markdown**: React Markdown

## التثبيت والتشغيل

1. تثبيت التبعيات:
```bash
npm install
```

2. تشغيل الخادم المحلي:
```bash
npm run dev
```

3. فتح المتصفح على العنوان: `http://localhost:5173`

## الحصول على مفتاح API

1. اذهب إلى [OpenRouter](https://openrouter.ai/)
2. قم بإنشاء حساب جديد أو تسجيل الدخول
3. اذهب إلى صفحة API Keys
4. أنشئ مفتاح API جديد
5. انسخ المفتاح واستخدمه في التطبيق

## الاستخدام

1. افتح التطبيق
2. اضغط على أيقونة الإعدادات (⚙️)
3. أدخل مفتاح OpenRouter API
4. اضغط "حفظ"
5. ابدأ المحادثة!

## البناء للإنتاج

```bash
npm run build
```

## الملفات المهمة

- `src/App.tsx` - المكون الرئيسي للتطبيق
- `src/index.css` - ملف الأنماط الرئيسي
- `tailwind.config.js` - إعدادات Tailwind CSS
- `vite.config.ts` - إعدادات Vite

## المساهمة

مرحب بالمساهمات! يرجى فتح issue أو pull request.

## الترخيص

MIT License
